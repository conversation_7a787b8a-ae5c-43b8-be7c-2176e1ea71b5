import 'dart:developer';
import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbols/symbols_bloc.dart';
import 'package:e_trader/src/presentation/symbols/widgets/search/empty_or_error_symbols_view.dart';
import 'package:e_trader/src/presentation/symbols/widgets/symbol_list_item.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SymbolListView extends StatelessWidget {
  const SymbolListView({
    super.key,
    this.emptyBuilder,
    this.errorBuilder,
    this.loadingBuilder,
    this.query,
    this.isSearchView = false,
  });
  final WidgetBuilder? emptyBuilder;
  final WidgetBuilder? errorBuilder;
  final WidgetBuilder? loadingBuilder;
  final String? query;
  final bool isSearchView;

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        BlocConsumer<SymbolsBloc, SymbolsState>(
          listenWhen:
              (previous, current) =>
                  previous.currentState != current.currentState,
          listener: (listenerContext, state) {
            if (state.currentState is SymbolsErrorState) {
              if (!Platform.environment.containsKey('FLUTTER_TEST')) {
                final toast = DuploToast();
                toast.hidesToastMessage();

                toast.showToastMessage(
                  autoCloseDuration: Duration.zero,
                  context: listenerContext,
                  widget: DuploToastMessage(
                    titleMessage:
                        EquitiLocalization.of(
                          listenerContext,
                        ).trader_loadingError,
                    descriptionMessage:
                        EquitiLocalization.of(
                          listenerContext,
                        ).trader_loadingErrorDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () => toast.hidesToastMessage(),
                    actionButtonTitle:
                        EquitiLocalization.of(listenerContext).trader_reload,
                    onTap: () {
                      log("print ----- on tab SymbolListView");

                      listenerContext.read<SymbolsBloc>().add(
                        SymbolsEvent.onGetSymbols(),
                      );
                      toast.hidesToastMessage();
                    },
                  ),
                );
              }
            }
          },
          buildWhen: (previous, current) => previous != current,
          builder: (builderContext, state) {
            final isSuccess = switch (state.currentState) {
              SymbolsSuccessState() => true,
              SymbolschangeTabState() => state.symbolsDetail.isNotEmpty,
              _ => false,
            };
            final isWatchList = state.selectedCategoryID == 'watchlist';
            final symbolsDetail =
                isSuccess
                    ? state.symbolsDetail
                    : <String, SymbolDetailViewModel>{};
            if (state.currentState is SymbolsLoadingState) {
              return SliverToBoxAdapter(
                child: Center(child: CircularProgressIndicator.adaptive()),
              );
            }

            return PagedView.sliver(
              physics:
                  (isSuccess && symbolsDetail.isEmpty) ||
                          state.currentState == SymbolsProcessState.error()
                      ? const NeverScrollableScrollPhysics()
                      : const ClampingScrollPhysics(),
              padding: EdgeInsets.zero,
              itemCount: isSuccess ? state.symbolsDetail.length : 0,
              centerError: true,
              centerLoading: true,
              centerEmpty: true,
              hasError: switch (state.currentState) {
                SymbolsErrorState() => true,
                _ => false,
              },
              isLoading: switch (state.currentState) {
                SymbolsLoadingState() => true,
                SymbolschangeTabState() => state.symbolsDetail.isEmpty,
                _ => false,
              },
              hasReachedMax: state.hasReachedMax,
              emptyBuilder:
                  isSuccess && symbolsDetail.isEmpty
                      ? (isWatchList
                          ? (ctx) => EmptyOrErrorSymbolsView(
                            message:
                                EquitiLocalization.of(
                                  context,
                                ).trader_emptyWatchlistDescription,
                            title:
                                EquitiLocalization.of(
                                  context,
                                ).trader_emptyWatchlist,
                            image: trader.Assets.images.emptyWatchlist.svg(),
                          )
                          : (emptyBuilder ??
                              (ctx) => EmptyOrErrorSymbolsView(
                                message:
                                    EquitiLocalization.of(
                                      context,
                                    ).trader_noSymbolsFound,
                                title:
                                    EquitiLocalization.of(
                                      context,
                                    ).trader_noMarkets,
                                image: trader.Assets.images.emptySearch.svg(),
                              )))
                      : null,
              loadingBuilder:
                  loadingBuilder ??
                  (ctx) =>
                      DuploShimmerList(hasLeading: true, hasTrailing: true),
              errorBuilder:
                  errorBuilder ??
                  (ctx) => EmptyOrErrorSymbolsView(
                    message:
                        EquitiLocalization.of(context).trader_marketsLoadFailed,
                    title:
                        EquitiLocalization.of(
                          context,
                        ).trader_somethingWentWrong,
                    image: trader.Assets.images.searchError.svg(),
                  ),
              separatorBuilder:
                  (ctx, index) => Divider(
                    color: context.duploTheme.border.borderSecondary,
                    height: 1,
                  ),
              onFetchData: () {
                if (context.mounted)
                  context.read<SymbolsBloc>().add(
                    SymbolsEvent.onGetSymbols(query: query),
                  );
              },
              itemBuilder: (ctx, index) {
                final symbolDetailMap = symbolsDetail.entries.elementAtOrNull(
                  index,
                );

                return SymbolListItem(
                  key: ValueKey(symbolDetailMap?.key),
                  symbol: symbolDetailMap!.value,
                  isSearchView: isSearchView,
                  onTap:
                      () => context.read<SymbolsBloc>().add(
                        SymbolsEvent.gotoDetails(
                          symbolDetail: symbolDetailMap.value,
                        ),
                      ),
                );
              },
            );
          },
        ),
      ],
    );
  }
}
